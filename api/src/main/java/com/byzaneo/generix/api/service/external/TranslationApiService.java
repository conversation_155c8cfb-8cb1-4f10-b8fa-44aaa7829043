package com.byzaneo.generix.api.service.external;

import com.byzaneo.generix.api.bean.*;
import com.byzaneo.generix.api.dto.translation.DictionaryImportResponse;
import io.swagger.annotations.*;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.*;
import javax.ws.rs.core.*;
import java.io.InputStream;
import java.util.UUID;

@Api(tags = "TranslationApiService", description = "Endpoints for managing Translation")
@Path(TranslationApiService.SERVICE_PATH)
public interface TranslationApiService {

  String SERVICE_NAME = "gnxRestTranslationApiService";
  String SERVICE_PATH = "v2/environments/translations";

  @GET
  @Path("/{dictType}")
  @Produces("application/octet-stream")
  @ApiOperation(
      value = "Retrieve the translation dictionary through a download of a CSV file",
      notes = "Downloads the translation dictionary as a CSV based on type and optional language filter. Supports both 'frontend' and 'errors' dictionary types."
  )
  @ApiResponses({
      @ApiResponse(code = 200, message = "OK - Returning the dictionary file"),
      @ApiResponse(code = 400, message = "Bad request", response = ErrorDTO.class),
      @ApiResponse(code = 403, message = "Forbidden", response = ErrorDTO.class),
      @ApiResponse(code = 500, message = "Internal server error", response = ErrorDTO.class),
      @ApiResponse(code = 503, message = "Service unavailable", response = ErrorDTO.class)
  })
  Response downloadDictionary(
      @Context HttpServletRequest request,
      @HeaderParam("requestId") UUID requestId,
      @PathParam("dictType") @ApiParam(required = true, allowableValues = "errors,frontend") String dictType,
      @QueryParam("lang") @ApiParam(required = false, value = "Single ISO 639-1 language code") String lang
  );



  @POST
  @Path("/{dictType}")
  @Consumes("text/csv")
  @Produces(MediaType.APPLICATION_JSON)
  @ApiOperation(
      value = "Create or update translations through the upload of a dictionary file",
      notes = "Allows importing translations by uploading a CSV file in the request body. Supports both 'frontend' and 'errors' dictionary types. Headers must include 'module', 'key', and language columns."
  )
  @ApiResponses({
      @ApiResponse(code = 201, message = "OK - Returning the status of the import", response = DictionaryImportResponse.class),
      @ApiResponse(code = 400, message = "Bad request", response = ErrorDTO.class),
      @ApiResponse(code = 403, message = "Forbidden", response = ErrorDTO.class),
      @ApiResponse(code = 409, message = "Conflict", response = ErrorDTO.class),
      @ApiResponse(code = 500, message = "Internal server error", response = ErrorDTO.class),
      @ApiResponse(code = 503, message = "Service unavailable", response = ErrorDTO.class)
  })
  Response importDictionary(
      @Context HttpServletRequest request,
      @HeaderParam("requestId") UUID requestId,
      @PathParam("dictType") @ApiParam(required = true, allowableValues = "errors,frontend") String dictType,
      @QueryParam("lang") @ApiParam(required = false, value = "Single ISO 639-1 language code") String lang,
      @ApiParam(value = "CSV file content", required = true) InputStream csvContent
  );
}
