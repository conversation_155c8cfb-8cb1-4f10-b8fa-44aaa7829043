package com.byzaneo.generix.api.service.external.delegators;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.core.Response;
import java.io.InputStream;
import java.util.UUID;

public interface TranslationServiceDelegator {

   Response downloadDictionary(HttpServletRequest request, UUID requestId, String dictType, String lang) ;

   Response importDictionary(HttpServletRequest request, UUID requestId, String dictType, String lang, InputStream csvContent);

}
