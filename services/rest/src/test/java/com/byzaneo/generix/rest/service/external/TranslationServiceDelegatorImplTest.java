/*
 * Copyright (c) 2025.
 *//*


package com.byzaneo.generix.rest.service.external;

import com.byzaneo.angular.bean.*;
import com.byzaneo.angular.dao.I18nTranslationDAO;
import com.byzaneo.generix.api.dto.translation.DictionaryImportResponse;
import com.byzaneo.generix.api.service.internal.impl.translation.TranslationService;
import com.byzaneo.generix.api.util.PermissionHelper;
import com.byzaneo.generix.service.repository.service.I18NService;
import com.byzaneo.generix.service.repository.service.translation.I18NTranslationDto;
import com.byzaneo.security.api.Right;
import com.byzaneo.security.bean.TechnicalUser;
import com.byzaneo.security.service.SecurityService;
import com.byzaneo.xtrade.service.DocumentErrorTranslationService;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockHttpServletRequest;

import javax.ws.rs.core.Response;
import java.io.ByteArrayInputStream;
import java.util.*;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

*/
/**
 * JDK 17 Enhanced Unit Tests for TranslationServiceDelegatorImpl
 *//*

@ExtendWith(MockitoExtension.class)
class TranslationServiceDelegatorImplTest {

    @Mock
    private SecurityService securityService;

    @Mock
    private PermissionHelper permissionHelper;

    @Mock
    private TranslationService translationService;

    @Mock
    private I18NService i18NService;

    @Mock
    private DocumentErrorTranslationService documentErrorTranslationService;

    @Mock
    private I18nTranslationDAO i18nTranslationDAO;

    @InjectMocks
    private TranslationServiceDelegatorImpl delegator;

    private MockHttpServletRequest request;
    private UUID requestId;
    private TechnicalUser technicalUser;

    @BeforeEach
    void setUp() {
        request = new MockHttpServletRequest();
        request.addHeader("Authorization", "Bearer test-token");
        requestId = UUID.randomUUID();
        
        technicalUser = new TechnicalUser();
        technicalUser.setId("1L");
        technicalUser.setDisabled(false);
    }

    @Test
    void testDownloadDictionary_Frontend_Success() {
        // JDK 17: Setup test data with var and modern collections
        var translations = List.of(
            createTranslationDto(1L, "common", "hello", "bonjour", "hello", Locale.FRENCH),
            createTranslationDto(1L, "common", "hello", "bonjour", "hello", Locale.ENGLISH)
        );
        var locales = List.of(Locale.FRENCH, Locale.ENGLISH);
        var modules = List.of(createModule(1L, "common"));

        // Mock security validation
        mockSecurityValidation();
        
        // Mock I18N service calls
        when(i18NService.getTranslations()).thenReturn(translations);
        when(i18NService.getLocales()).thenReturn(locales);
        when(i18NService.findAllI18NModules()).thenReturn(modules);

        // When
        var response = delegator.downloadDictionary(request, requestId, "frontend", "fr");

        // Then
        assertEquals(Response.Status.OK.getStatusCode(), response.getStatus());
        assertNotNull(response.getEntity());
        assertTrue(response.getHeaders().containsKey("Content-Disposition"));
        
        // Verify CSV content
        var csvBytes = (byte[]) response.getEntity();
        var csvContent = new String(csvBytes);
        assertTrue(csvContent.contains("module;key"));
        assertTrue(csvContent.contains("common;hello"));
    }

    @Test
    void testDownloadDictionary_Errors_Success() {
        // Mock security validation
        mockSecurityValidation();
        
        // Mock error translation service
        var mockResponse = Response.ok("error,csv,content".getBytes()).build();
        when(translationService.exportTranslationsWithLang("es")).thenReturn(mockResponse);

        // When
        var response = delegator.downloadDictionary(request, requestId, "errors", "es");

        // Then
        assertEquals(Response.Status.OK.getStatusCode(), response.getStatus());
        verify(translationService).exportTranslationsWithLang("es");
    }

    @Test
    void testImportDictionary_Frontend_Success() {
        // JDK 17: Test data with text blocks
        var csvContent = """
            module;key;fr;en;de
            common;hello;bonjour;hello;hallo
            common;goodbye;au revoir;goodbye;auf wiedersehen
            """;
        var inputStream = new ByteArrayInputStream(csvContent.getBytes());
        var modules = List.of(createModule(1L, "common"));

        // Mock security validation
        mockSecurityValidation();
        
        // Mock I18N service calls
        when(i18NService.findAllI18NModules()).thenReturn(modules);
        when(i18NService.findI18NModuleByName("common")).thenReturn(Optional.of(createModule(1L, "common")));
        when(i18NService.findI18NModuleById(1L)).thenReturn(createModule(1L, "common"));
        when(i18nTranslationDAO.search(any())).thenReturn(Collections.emptyList());
        when(i18nTranslationDAO.store(any())).thenReturn(new I18NTranslation());

        // When
        var response = delegator.importDictionary(request, requestId, "frontend", "de", inputStream, null);

        // Then
        assertEquals(Response.Status.OK.getStatusCode(), response.getStatus());
        assertNotNull(response.getEntity());
        
        var importResponse = (DictionaryImportResponse) response.getEntity();
        assertTrue(importResponse.getTotalImported() > 0);
    }

    @Test
    void testImportDictionary_Errors_Success() {
        // Mock security validation
        mockSecurityValidation();
        
        var csvContent = "module;key;it\nerrors;ERR001;Errore";
        var inputStream = new ByteArrayInputStream(csvContent.getBytes());
        var mockResponse = Response.ok(DictionaryImportResponse.builder()
            .totalImported(1)
            .errors(Collections.emptyList())
            .build()).build();
            
        when(translationService.importTranslationsWithLang("it", inputStream)).thenReturn(mockResponse);

        // When
        var response = delegator.importDictionary(request, requestId, "errors", "it", inputStream, null);

        // Then
        assertEquals(Response.Status.OK.getStatusCode(), response.getStatus());
        verify(translationService).importTranslationsWithLang("it", inputStream);
    }

    @Test
    void testValidateRequest_InvalidDictType() {
        // Mock security setup
        when(securityService.getTechnicalUserById(anyString())).thenReturn(technicalUser);
        when(permissionHelper.isGrantedTechnicalUser(any(), eq(Right.READ), eq(technicalUser))).thenReturn(true);

        // When
        var response = delegator.downloadDictionary(request, requestId, "invalid", null);

        // Then
        assertEquals(Response.Status.BAD_REQUEST.getStatusCode(), response.getStatus());
    }

    @Test
    void testValidateRequest_MissingAuth() {
        // Given - request without authorization header
        var requestWithoutAuth = new MockHttpServletRequest();

        // When
        var response = delegator.downloadDictionary(requestWithoutAuth, requestId, "frontend", null);

        // Then
        assertEquals(Response.Status.FORBIDDEN.getStatusCode(), response.getStatus());
    }

    @Test
    void testParseAndValidateLangCode_InvalidLanguage() {
        // Mock security validation
        mockSecurityValidation();

        // When & Then
        assertThrows(IllegalArgumentException.class, () -> {
            delegator.downloadDictionary(request, requestId, "frontend", "invalid");
        });
    }

    // JDK 17: Helper methods with modern syntax
    private void mockSecurityValidation() {
        when(securityService.getTechnicalUserById(anyString())).thenReturn(technicalUser);
        when(permissionHelper.isGrantedTechnicalUser(any(), any(Right.class), eq(technicalUser))).thenReturn(true);
    }

    private I18NTranslationDto createTranslationDto(Long moduleId, String module, String code, String frValue, String enValue, Locale locale) {
        return I18NTranslationDto.builder()
            .id(1L)
            .code(code)
            .newValue(locale.equals(Locale.FRENCH) ? frValue : enValue)
            .defaultValue(locale.equals(Locale.FRENCH) ? frValue : enValue)
            .locale(locale)
            .i18NModuleId(moduleId)
            .build();
    }

    private I18NModule createModule(Long id, String name) {
        var module = new I18NModule();
        module.setId(id);
        module.setName(name);
        return module;
    }
}
*/
